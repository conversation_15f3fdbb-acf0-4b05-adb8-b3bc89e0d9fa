package main

import (
	"fmt"
	"net/http"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
)

func main() {
	r := gin.Default()

	r.GET("/*path", func(c *gin.Context) {
		p := c.Param("path")
		fmt.Printf("Original path: %s\n", p)
		if strings.Contains(p, "..") {
			c.AbortWithStatus(400)
			c.String(400, "URL path cannot contain \"..\"")
			return
		}
		// Some people were confused and were putting /attachments in the URLs. This fixes that
		afterReplace := strings.ReplaceAll(p, "/attachments", "")
		fmt.Printf("After ReplaceAll: %s\n", afterReplace)
		afterClean := filepath.Clean(afterReplace)
		fmt.Printf("After Clean: %s\n", afterClean)
		cleanPath := filepath.Join("./attachments", afterClean)
		fmt.Printf("Final path: %s\n", cleanPath)
		http.ServeFile(c<PERSON><PERSON>, c<PERSON>Request, cleanPath)
	})

	r.<PERSON>("0.0.0.0:1337")
}
